/*
 * PortfolioPage.tsx – <PERSON><PERSON><PERSON>'s interactive portfolio
 * Built with Next.js/React, Tailwind CSS, and Framer-Motion.
 * ----------------------------------------------------------------------------
 * Quick-start
 * 1. npx create-next-app@latest my-portfolio --typescript --tailwind
 * 2. cd my-portfolio && npm i framer-motion lucide-react
 * 3. Drop this file into app/page.tsx (Next 13+) or pages/index.tsx (≤12).
 * 4. Copy the bg-grid utility (bottom) into globals.css.
 * 5. npm run dev 🚀
 */

'use client';

import { motion, useScroll, useSpring } from 'framer-motion';
import { ReactNode } from 'react';
import { Github, Linkedin, Mail, Twitter } from 'lucide-react';
import Image from 'next/image';
import PageProgress from './components/PageProgress';
import SkillsGraph from './components/SkillsGraph';


/* ----------------------------- Helpers ----------------------------- */
const Section = ({ children }: { children: ReactNode }) => (
  <motion.section
    className="w-full max-w-4xl mx-auto py-12 px-6 sm:px-8 md:px-10 lg:px-12 xl:px-16"
    initial={{ opacity: 0, y: 60 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true, amount: 0.35 }}
    transition={{ duration: 0.8, ease: 'easeOut' }}
  >
    {children}
  </motion.section>
);

/* ------------------------ Reusable cards -------------------------- */
interface ExperienceCardProps {
  title: string;
  company: string;
  dates: string;
  location: string;
  description: string;
  tech: string[];
  links?: { href: string; label: string }[];
  icon?: ReactNode; // Optional icon prop
}
const ExperienceCard = ({ title, company, dates, location, description, tech, links, icon }: ExperienceCardProps) => (
  <div className="relative rounded-2xl bg-[#18191b] border border-blue-200/10 p-8 flex flex-col gap-4 shadow-lg">
    {/* Accent bar on the left */}
    <div className="absolute left-0 top-6 bottom-6 w-1 rounded-full bg-gradient-to-b from-blue-400 via-cyan-500 to-blue-400 opacity-60 animate-pulse-slow" />
    <div className="flex flex-col sm:flex-row gap-4 min-h-[3rem]">
      <div className="flex items-start sm:items-center justify-center w-12 h-12 rounded-xl mt-1 sm:mt-0">
        <span className="text-2xl text-blue-400">
          {icon ? icon : (
            // Default icon
            <svg width="28" height="28" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-text"><path d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'/><polyline points='14 2 14 8 20 8'/><line x1='16' x2='8' y1='13' y2='13'/><line x1='16' x2='8' y1='17' y2='17'/><polyline points='10 9 9 9 8 9'/></svg>
          )}
        </span>
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex flex-wrap items-center gap-2">
          <h3 className="text-xl font-bold text-white leading-tight">{title}</h3>
          {company && <span className="text-md text-gray-400 font-medium">&bull; {company}</span>}
        </div>
        <div className="text-gray-400 text-base mt-1">{dates}</div>
      </div>
    </div>
    <div className="text-gray-400 text-base pl-16 -mt-2">{description}</div>
    <div className="flex flex-wrap gap-2 pl-16 mt-1">
      {tech.map(t => (
        <span key={t} className="bg-[#232428] text-gray-100 px-3 py-1 rounded-lg text-xs font-medium">{t}</span>
      ))}
    </div>
    {links?.length && (
      <div className="flex gap-3 pl-16 mt-2">
        {links.map(l => (
          <a key={l.href} href={l.href} target="_blank" rel="noreferrer" className="text-sm text-blue-400 hover:underline">{l.label}</a>
        ))}
      </div>
    )}
    <div className="pointer-events-none absolute inset-0 rounded-2xl border border-white/5" style={{boxShadow: '0 2px 24px 0 #0008'}}></div>
  </div>
);

interface ProjectCardProps {
  title: string;
  description: string;
  tech: string[];
  status?: string;
  links?: { href: string; label: string }[];
}
const ProjectCard = ({ title, description, tech, status, links }: ProjectCardProps) => (
  <div className="relative rounded-2xl bg-[#18191b] border border-white/10 p-7 flex flex-col gap-4 shadow-lg">
    {/* Light blue-green accent bar on the left, matching ExperienceCard */}
    <div className="absolute left-0 top-6 bottom-6 w-1 rounded-full bg-gradient-to-b from-blue-400 via-cyan-300 to-blue-400 opacity-60 animate-pulse-slow" />
    <div className="flex items-start gap-4 ml-3">
      <div className="flex-1">
        <h3 className="text-xl font-semibold text-white leading-tight">{title}</h3>
        {status && <span className="text-xs bg-blue-400/10 text-blue-200 px-2 py-1 rounded-full whitespace-nowrap ml-2">{status}</span>}
      </div>
    </div>
    <div className="text-gray-400 text-base ml-3">{description}</div>
    <div className="flex flex-wrap gap-2 mt-2 ml-3">
      {tech.map(t => (
        <span key={t} className="text-xs bg-blue-400/10 text-blue-100 px-3 py-1 rounded-full tracking-wide">{t}</span>
      ))}
    </div>
    {links?.length && (
      <div className="flex gap-3 mt-2 ml-3">
        {links.map(l => (
          <a key={l.href} href={l.href} target="_blank" rel="noreferrer" className="text-sm text-blue-200 hover:underline">{l.label}</a>
        ))}
      </div>
    )}
    {/* Card shadow and border effect */}
    <div className="pointer-events-none absolute inset-0 rounded-2xl border border-white/5" style={{boxShadow: '0 2px 24px 0 #0008'}}></div>
  </div>
);

interface BlogCardProps {
  title: string;
  date: string;
  excerpt: string;
}
const BlogCard = ({ title, date, excerpt }: BlogCardProps) => (
  <div className="relative rounded-2xl bg-[#18191b] border border-white/10 p-7 flex flex-col gap-4 shadow-lg">
    {/* Accent bar on the left */}
    <div className="absolute left-0 top-6 bottom-6 w-1 rounded-full bg-gradient-to-b from-blue-400 to-cyan-600" />
    <div className="ml-3">
      <h3 className="text-lg font-semibold text-white mb-1">{title}</h3>
      <p className="text-sm text-gray-400 mb-3">{date}</p>
      <p className="text-gray-400">{excerpt}</p>
    </div>
    {/* Card shadow and border effect */}
    <div className="pointer-events-none absolute inset-0 rounded-2xl border border-white/5" style={{boxShadow: '0 2px 24px 0 #0008'}}></div>
  </div>
);

/* ----------------------------- Page ------------------------------- */
import { useEffect, useState } from 'react';

function BlogsSection() {
  const [posts, setPosts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch('/api/medium')
      .then(res => res.json())
      .then(data => {
        setPosts(data);
        setLoading(false);
      });
  }, []);

  return (
    <Section>
      <h2 className="text-3xl font-semibold mb-8 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">Blogs</h2>
      {loading ? (
        <div className="text-gray-400">Loading...</div>
      ) : (
        <div className="flex flex-col gap-5">
          {posts.map(post => (
            <a key={post.link} href={post.link} target="_blank" rel="noopener noreferrer">
              <div className="rounded-2xl bg-[#18191b] border border-white/10 p-7 shadow-lg hover:border-blue-400 transition flex flex-col md:flex-row gap-6">
                {post.image && (
                  <img
                    src={post.image}
                    alt={post.title}
                    className="w-full md:w-48 h-40 object-cover rounded-xl mb-4 md:mb-0"
                    style={{ maxWidth: 192, minWidth: 120, background: '#222' }}
                  />
                )}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">{post.title}</h3>
                  <p className="text-sm text-gray-400 mb-2">{new Date(post.pubDate).toLocaleDateString()}</p>
                  {post.subtitle && (
                    <p className="text-gray-300 mb-2">{post.subtitle}</p>
                  )}
                  <p className="text-gray-500">{post.contentSnippet}</p>
                </div>
              </div>
            </a>
          ))}
        </div>
      )}
    </Section>
  );
}

export default function PortfolioPage() {
  return (
    <>
      <div className="fixed inset-0 -z-10 bg-grid" aria-hidden="true"></div>
      <main className="relative min-h-screen text-gray-100 scroll-smooth w-full max-w-screen-xl mx-auto px-2 sm:px-4 md:px-8 lg:px-12 xl:px-16 mt-8">
        <PageProgress />
        {/* --------------------------- HERO --------------------------- */}
        <Section>
          <div className="relative flex flex-col items-start text-left gap-1 w-full">
            {/* Top-right icons for desktop */}
            <div className="absolute top-0 right-0 hidden md:flex gap-7 z-10">
              <div className="relative group flex flex-col items-center">
                <a href="https://github.com/dorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform">
                  <Github className="w-7 h-7 text-white" />
                </a>
                <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">GitHub</span>
              </div>
              <div className="relative group flex flex-col items-center">
                <a href="https://linkedin.com/in/dorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform">
                  <Linkedin className="w-7 h-7 text-white" />
                </a>
                <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">LinkedIn</span>
              </div>
              <div className="relative group flex flex-col items-center">
                <a href="mailto:<EMAIL>" className="hover:scale-110 transition-transform">
                  <Mail className="w-7 h-7 text-white" />
                </a>
                <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">Email</span>
              </div>
              <div className="relative group flex flex-col items-center">
                <a href="https://x.com/itsdorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform">
                  <Twitter className="w-7 h-7 text-white" />
                </a>
                <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">Twitter/X</span>
              </div>
            </div>
            <div className="flex-shrink-0 w-40 h-40 rounded-2xl overflow-hidden mb-6">
              <Image
                src="/avatar-sid.jpg"
                alt="Siddharth"
                width={160}
                height={160}
                unoptimized
                style={{ objectFit: 'cover', width: '100%', height: '100%' }}
              />
            </div>
            <div className="flex-1 flex flex-col items-start gap-2">
              {/* Icons above name for mobile */}
              <div className="flex md:hidden gap-7 justify-start mb-2">
                <div className="relative group flex flex-col items-center">
                  <a href="https://github.com/dorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform">
                    <Github className="w-7 h-7 text-white" />
                  </a>
                  <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">GitHub</span>
                </div>
                <div className="relative group flex flex-col items-center">
                  <a href="https://linkedin.com/in/dorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform">
                    <Linkedin className="w-7 h-7 text-white" />
                  </a>
                  <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">LinkedIn</span>
                </div>
                <div className="relative group flex flex-col items-center">
                  <a href="mailto:<EMAIL>" className="hover:scale-110 transition-transform">
                    <Mail className="w-7 h-7 text-white" />
                  </a>
                  <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">Email</span>
                </div>
                <div className="relative group flex flex-col items-center">
                  <a href="https://x.com/itsdorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform">
                    <Twitter className="w-7 h-7 text-white" />
                  </a>
                  <span className="absolute -top-7 left-1/2 -translate-x-1/2 px-2 py-1 rounded bg-black/80 text-xs text-white opacity-0 group-hover:opacity-100 group-hover:-translate-y-2 transition-all duration-200 pointer-events-none select-none">Twitter/X</span>
                </div>
              </div>
              <h1 className="text-4xl font-normal text-white">
                Hi, I'm Siddharth
              </h1>
              <p className="text-xl text-gray-400">22, Mumbai · Software Engineer</p>
              <p className="max-w-2xl text-md text-gray-400">
                I'm a polyglot crafting AI agentic solutions and scalable backend systems. From building
                distributed web-scraping platforms to developing interactive fitness analytics. I build based on requirements, not instructions.
              </p>
            </div>
          </div>
        </Section>

        {/* ----------------------- EXPERIENCE ------------------------- */}
        <Section>
          <h2 className="text-3xl font-semibold mb-2 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">Experience</h2>
          <p className="mb-8 text-gray-400 text-base">Roles and projects that shaped my journey as a developer.</p>
          <div className="space-y-10">
            <ExperienceCard
              title="Lead Developer"
              company="(Full Stack & Cloud Infrastructure)"
              dates="Nov 2024 – Feb 2025"
              location="Mumbai, India"
              description="Pioneered India's first fully streamlined electoral-sampling automation."
              tech={['Python', 'AWS (EC2, S3)', 'Selenium', 'Tesseract', 'EasyOCR', 'Multi-threading']}
              links={[{ href: 'https://edownloaders.com', label: 'edownloaders.com' }]}
              icon={
                // Cloud/server icon
                <svg width="28" height="28" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-cloud"><path d="M17.5 19a4.5 4.5 0 1 0-9 0"/><path d="M22 17.5A5.5 5.5 0 0 0 12.5 7a7 7 0 0 0-7 7.5"/></svg>
              }
            />

            <ExperienceCard
              title="Software Intern"
              company="(Business Development Automation)"
              dates="Jun 2024 – Jan 2025 (Current)"
              location="Thane, India"
              description="Executed comprehensive website testing and business automation."
              tech={['Google Sheets Apps Script', 'Pabbly', 'WhatsApp Business API']}
              icon={
                // Workflow/automation icon
                <svg width="28" height="28" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-zap"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/></svg>
              }
            />
          </div>
        </Section>

        {/* ------------------------ PROJECTS -------------------------- */}
        <Section>
          <h2 className="text-3xl font-semibold mb-8 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">Projects</h2>
          {/* Tabs are omitted for brevity – simply list main projects */}
          <div className="flex flex-col gap-5">
            <ProjectCard
              title="Gymzy – AI Fitness Analytics & Social Platform"
              status="Live"
              description="Interactive AI-based fitness analytics and workout logging with social integration."
              tech={['Next.js', 'TypeScript', 'Node.js', 'AI Integration']}
              links={[{ href: 'https://gymzy.vercel.app', label: 'Live Demo' }, { href: 'https://github.com/dorddis/gymzy', label: 'GitHub' }]}
            />
            <ProjectCard
              title="EggyPro – E-commerce Platform"
              status="Live"
              description="Modern e-commerce frontend with AI-powered customer support assistant."
              tech={['Next.js 15', 'TypeScript', 'Google Gemini AI', 'Genkit']}
              links={[{ href: 'https://eggypro.com', label: 'Live Demo' }]}
            />
            <ProjectCard
              title="AI-assisted Coding & Debugging"
              description="Accelerated software development through innovative AI-model implementation."
              tech={['o1', 'o3', 'o4-mini', 'Prompt Engineering']}
              links={[{ href: 'https://github.com/dorddis/ai-coding', label: 'GitHub' }]}
            />
          </div>
        </Section>

        {/* ------------------------- FRAMEWORKS & TECHNOLOGIES -------------------------- */}
        
        <Section>
          <h2 className="text-3xl font-semibold mb-8 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">
            Tech I work with
          </h2>
          {/* Interactive force-graph */}
          <SkillsGraph />
        </Section>

        {/* ---------------- EDUCATION & ACTIVITIES ------------------ */}
        <Section>
          <h2 className="text-3xl font-semibold mb-8 bg-gradient-to-r from-blue-400 to-cyan-600 text-transparent bg-clip-text">Education & Activities</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {/* Education Card */}
            <div className="relative rounded-2xl bg-[#18191b] border border-white/10 p-6 shadow-lg flex flex-col gap-2">
              {/* Dimmed accent bar for research */}
              <div className="absolute left-0 top-6 bottom-6 w-1 rounded-full bg-gradient-to-b from-fuchsia-400 to-blue-400 opacity-60 animate-pulse-slow" />
              <h3 className="text-xl font-semibold text-white mb-1">Indian Institute of Information Technology, Pune</h3>
              <p className="text-sm text-gray-400 mb-2">B.Tech. in Computer Science & Engineering · CGPA 8.14/10 · Graduated</p>
              <p className="text-gray-300 mb-1">Key Courses: C++, Java, Python, Statistics, DSA, DBMS, OOP, ML, Cloud Computing, Big Data, HPC & Distributed Computing</p>
              <div className="mt-2">
                <p className="text-gray-300 font-semibold">Research: Deep-learning model with 28 dB average PSNR improvement & 36 % OCR improvement</p>
                <div className="mt-3">
                  <span className="block text-fuchsia-300 font-bold mb-1">Research Paper Preview:</span>
                  <a
                    href="https://drive.google.com/file/d/17-Aeh-dMh-h2bUWoHXhkb1iaM8y5sEf0/view?usp=drive_link"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block rounded-lg overflow-hidden border border-fuchsia-400/30 shadow-md focus:outline-none focus:ring-2 focus:ring-fuchsia-400/60"
                    title="Open full research paper in Google Drive"
                  >
                    <iframe
                      src="/research-paper.pdf"
                      width="100%"
                      height="185"
                      className="w-full min-h-[185px] bg-black hide-scrollbar pointer-events-none"
                      title="Research Paper Preview"
                      tabIndex={-1}
                    ></iframe>
                  </a>
                </div>
              </div>
            </div>
            {/* Activities Cards */}
            <div className="flex flex-col gap-6">
              <div className="rounded-2xl bg-[#18191b] border border-white/10 p-6 shadow-lg flex flex-col gap-2">
                <h4 className="text-lg font-semibold text-white mb-1">E-Cell IIIT Pune – Marketing Associate</h4>
                <p className="text-gray-300">Pitched sponsorships for E-Summit 2k23 (20,000 + participants), managed events, and automated payment verification.</p>
              </div>
              <div className="rounded-2xl bg-[#18191b] border border-white/10 p-6 shadow-lg flex flex-col gap-2">
                <h4 className="text-lg font-semibold text-white mb-1">Hackathon Achievement</h4>
                <p className="text-gray-300">4th place (western region) in "Solving For India" hackathon (2,000 + teams). Built blockchain health-record NFTs; selected by Google to share experience.</p>
              </div>
              <div className="rounded-2xl bg-[#18191b] border border-white/10 p-6 shadow-lg flex flex-col gap-2">
                <h4 className="text-lg font-semibold text-white mb-1">Academic Achievements</h4>
                <p className="text-gray-300">JEE 2021 – 97.9 percentile (99.5 Math), Class Representative – Batch of '25, Intermediate score 87.7 %.</p>
              </div>
            </div>
          </div>
        </Section>
        
      <BlogsSection />

        {/* ------------------------- CONTACT ------------------------- */}
        <Section>
          <div className="flex flex-col md:flex-row items-center md:items-end justify-between gap-6 md:gap-0">
            {/* Name on the left */}
            <div className="w-full md:w-auto text-center md:text-left">
              <span className="text-sm font-normal text-gray-400">Siddharth</span>
            </div>
            {/* Icons for contact links on the right */}
            <div className="flex flex-row gap-7 justify-center md:justify-end w-full md:w-auto">
              <a href="mailto:<EMAIL>" className="hover:scale-110 transition-transform" title="Email">
                <Mail className="w-7 h-7 text-gray-500" />
              </a>
              <a href="https://app.cal.com/dorddis-work" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform" title="Book a Meeting">
                {/* Calendar/Meeting icon */}
                <svg xmlns="http://www.w3.org/2000/svg" className="w-7 h-7 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2"><rect x="3" y="4" width="18" height="18" rx="2"/><path d="M16 2v4M8 2v4M3 10h18"/></svg>
              </a>
              <a href="https://github.com/dorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform" title="GitHub">
                <Github className="w-7 h-7 text-gray-500" />
              </a>
              <a href="https://linkedin.com/in/dorddis" target="_blank" rel="noreferrer" className="hover:scale-110 transition-transform" title="LinkedIn">
                <Linkedin className="w-7 h-7 text-gray-500" />
              </a>
            </div>
          </div>
        </Section>

      </main>
    </>
  );
}

/* -------------------------------------------------------------------------
   Tailwind extension: subtle grid background (add to globals.css)
   -------------------------------------------------------------------------
   @layer utilities {
     .bg-grid {
       @apply bg-black;
       background-image:
         linear-gradient(to right, rgba(255,255,255,0.05) 1px, transparent 1px),
         linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
       background-size: 40px 40px;
       background-attachment: fixed;
     }
   }
*/