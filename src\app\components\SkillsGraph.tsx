/* SkillsGraph.tsx – 2-D force graph, pill nodes, strict-TS safe
   ------------------------------------------------------------- */
'use client';

import dynamic from 'next/dynamic';
import { useWindowSize } from '@uidotdev/usehooks';
import { useEffect, useMemo, useRef, useCallback } from 'react';
import type { ForceGraphMethods } from 'react-force-graph-2d';
import * as d3 from 'd3-force';

/* ---------- lazy component ---------- */
const ForceGraph2D = dynamic(
  () => import('react-force-graph-2d').then(m => m.default),
  { ssr: false }
);

/* ---------- data ---------- */
type Node = { id: string; group: string; fx?: number; fy?: number };
type Link = { source: string; target: string };

const cats = [
  'Programming Languages',
  'Frameworks & Tech',
  'Machine Learning & AI',
  'Cloud & DevOps'
] as const;
type Cat = typeof cats[number];
const isCat = (id: string): id is Cat => (cats as readonly string[]).includes(id);

const skills: Record<Cat, string[]> = {
  'Programming Languages': ['Python (primary)', 'Java', 'C++'],
  'Frameworks & Tech': [
    'Django', 'Node.js', 'FastAPI', 'React.js', 'Angular',
    'Next.js', 'Tailwind CSS', 'TypeScript', 'MongoDB',
    'MySQL', 'SQLite3'
  ],
  'Machine Learning & AI': [
    'TensorFlow', 'PyTorch', 'Scikit-learn', 'Transformers',
    'CNNs', 'RNNs', 'OpenAI API', 'Hugging Face'
  ],
  'Cloud & DevOps': [
    'AWS (EC2, S3, Lambda)', 'Docker', 'Kubernetes',
    'CI/CD (Jenkins, GitHub Actions)'
  ]
};

const colorOf: Record<Cat, string> = {
  'Programming Languages': '#3b82f6',
  'Frameworks & Tech':     '#06b6d4',
  'Machine Learning & AI': '#d946ef',
  'Cloud & DevOps':        '#ea580c'
};

function buildGraph() {
  const nodes: Node[] = [];
  const links: Link[] = [];

  cats.forEach(cat => {
    nodes.push({ id: cat, group: cat });
    skills[cat].forEach(skill => {
      nodes.push({ id: skill, group: cat });
      links.push({ source: cat, target: skill });
    });
  });

  cats.forEach(cat => {
    const list = skills[cat];
    list.forEach((skill, i) =>
      links.push({ source: skill, target: list[(i + 1) % list.length] })
    );
  });

  ([
    ['Python (primary)', 'Django'], ['Python (primary)', 'FastAPI'],
    ['Python (primary)', 'TensorFlow'], ['Python (primary)', 'PyTorch'],
    ['Python (primary)', 'Scikit-learn'], ['Java', 'MySQL'],
    ['Java', 'AWS (EC2, S3, Lambda)'], ['Node.js', 'TypeScript'],
    ['Node.js', 'MongoDB'], ['Node.js', 'Docker'],
    ['React.js', 'Next.js'], ['Docker', 'Kubernetes'],
    ['Docker', 'CI/CD (Jenkins, GitHub Actions)'],
    ['Kubernetes', 'AWS (EC2, S3, Lambda)'],
    ['TensorFlow', 'CNNs'], ['PyTorch', 'Transformers'],
    ['OpenAI API', 'Transformers'], ['MongoDB', 'AWS (EC2, S3, Lambda)'],
    ['MySQL', 'CI/CD (Jenkins, GitHub Actions)']
  ] as [string, string][]).forEach(([a, b]) =>
    links.push({ source: a, target: b })
  );

  return { nodes, links };
}

/* ---------- component ---------- */
export default function SkillsGraph() {
  const { width: winW = 800 } = useWindowSize();
  const fgRef = useRef<ForceGraphMethods<Node, Link>>(null);

  const data = useMemo(buildGraph, []);

  /* physics setup */
  useEffect(() => {
    const fg = fgRef.current;
    if (!fg) return;

    fg.d3Force('charge')!.strength(-700).distanceMax(1000);

    fg.d3Force('link')!
      .distance(l => (isCat((l.source as Node).id) ? 320 : 260))
      .strength(l => (isCat((l.source as Node).id) ? 0.3 : 0.15));

    fg.d3Force('collide', d3.forceCollide<Node>().radius(n => isCat(n.id) ? 95 : 75) as any);

    fg.warmupTicks(400).cooldownTicks(Infinity);
  }, []);

  /* pill renderer (big, draggable) */
  const draw = useCallback((
    n: Node,
    ctx: CanvasRenderingContext2D,
    s: number,
    mask?: string
  ) => {
    if (n.x == null || n.y == null) return;      // not yet positioned

    const font = 18 / s, padX = 12 / s, padY = 6 / s;
    ctx.font = `600 ${font}px Inter, sans-serif`;
    const w = ctx.measureText(n.id).width + padX * 2;
    const h = font + padY * 2;
    const x = n.x - w / 2, y = n.y - h / 2, r = 9 / s;

    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.arcTo(x + w, y,     x + w, y + h, r);
    ctx.arcTo(x + w, y + h, x,     y + h, r);
    ctx.arcTo(x,     y + h, x,     y,     r);
    ctx.arcTo(x,     y,     x + w, y,     r);
    ctx.closePath();

    if (mask) { ctx.fillStyle = mask; ctx.fill(); return; }

    const col = colorOf[n.group as Cat];
    ctx.fillStyle   = `${col}22`;
    ctx.strokeStyle = col;
    ctx.lineWidth   = 1 / s;
    ctx.fill(); ctx.stroke();

    ctx.fillStyle = col;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(n.id, n.x, n.y);
  }, []);

  return (
    <div className="mx-auto" style={{ width: Math.min(winW, 800) }}>
      <ForceGraph2D
        ref={fgRef}
        width={Math.min(winW, 800)}
        height={600}
        backgroundColor="rgba(0,0,0,0)"
        graphData={data}
        nodeCanvasObject={(n, ctx, s) => draw(n as Node, ctx, s)}
        nodePointerAreaPaint={(n, c, ctx, s) => draw(n as Node, ctx, s, c)}
        linkColor={() => '#334155'}
        linkWidth={0.6}

        enableNodeDrag
        onNodeDrag={n => { n.fx = n.x; n.fy = n.y; fgRef.current?.d3ReheatSimulation(); }}
        onNodeDragEnd={n => { n.fx = n.fy = undefined; fgRef.current?.d3ReheatSimulation(); }}
      />
    </div>
  );
}
