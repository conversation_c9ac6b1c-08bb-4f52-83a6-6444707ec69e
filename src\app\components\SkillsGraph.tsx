/* SkillsGraph.tsx – Enhanced 2-D physics-based skills graph with scroll gravity
   ---------------------------------------------------------------------------- */
'use client';

import dynamic from 'next/dynamic';
import { useWindowSize } from '@uidotdev/usehooks';
import { useEffect, useMemo, useRef, useCallback, useState } from 'react';
import type { ForceGraphMethods } from 'react-force-graph-2d';
import * as d3 from 'd3-force';

/* ---------- lazy component ---------- */
const ForceGraph2D = dynamic(
  () => import('react-force-graph-2d').then(m => m.default),
  { ssr: false }
);

/* ---------- types ---------- */
type Node = {
  id: string;
  group: string;
  fx?: number;
  fy?: number;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
};
type Link = { source: string; target: string };

interface PhysicsControls {
  nodeSizeMultiplier: number;
  repulsionStrength: number;
  categoryLinkDistance: number;
  skillLinkDistance: number;
  categoryLinkStrength: number;
  skillLinkStrength: number;
  boundaryStrength: number;
  gravityStrength: number;
  massMultiplier: number;
  cohesionStrength: number;
}

const cats = [
  'Programming Languages',
  'Frameworks & Tech',
  'Machine Learning & AI',
  'Cloud & DevOps'
] as const;
type Cat = typeof cats[number];
const isCat = (id: string): id is Cat => (cats as readonly string[]).includes(id);

const skills: Record<Cat, string[]> = {
  'Programming Languages': ['Python (primary)', 'Java', 'C++'],
  'Frameworks & Tech': [
    'Django', 'Node.js', 'FastAPI', 'React.js', 'Angular',
    'Next.js', 'Tailwind CSS', 'TypeScript', 'MongoDB',
    'MySQL', 'SQLite3'
  ],
  'Machine Learning & AI': [
    'TensorFlow', 'PyTorch', 'Scikit-learn', 'Transformers',
    'CNNs', 'RNNs', 'OpenAI API', 'Hugging Face'
  ],
  'Cloud & DevOps': [
    'AWS (EC2, S3, Lambda)', 'Docker', 'Kubernetes',
    'CI/CD (Jenkins, GitHub Actions)'
  ]
};

const colorOf: Record<Cat, string> = {
  'Programming Languages': '#3b82f6',
  'Frameworks & Tech':     '#06b6d4',
  'Machine Learning & AI': '#d946ef',
  'Cloud & DevOps':        '#ea580c'
};

function buildGraph() {
  const nodes: Node[] = [];
  const links: Link[] = [];

  cats.forEach(cat => {
    nodes.push({ id: cat, group: cat });
    skills[cat].forEach(skill => {
      nodes.push({ id: skill, group: cat });
      links.push({ source: cat, target: skill });
    });
  });

  cats.forEach(cat => {
    const list = skills[cat];
    list.forEach((skill, i) =>
      links.push({ source: skill, target: list[(i + 1) % list.length] })
    );
  });

  ([
    ['Python (primary)', 'Django'], ['Python (primary)', 'FastAPI'],
    ['Python (primary)', 'TensorFlow'], ['Python (primary)', 'PyTorch'],
    ['Python (primary)', 'Scikit-learn'], ['Java', 'MySQL'],
    ['Java', 'AWS (EC2, S3, Lambda)'], ['Node.js', 'TypeScript'],
    ['Node.js', 'MongoDB'], ['Node.js', 'Docker'],
    ['React.js', 'Next.js'], ['Docker', 'Kubernetes'],
    ['Docker', 'CI/CD (Jenkins, GitHub Actions)'],
    ['Kubernetes', 'AWS (EC2, S3, Lambda)'],
    ['TensorFlow', 'CNNs'], ['PyTorch', 'Transformers'],
    ['OpenAI API', 'Transformers'], ['MongoDB', 'AWS (EC2, S3, Lambda)'],
    ['MySQL', 'CI/CD (Jenkins, GitHub Actions)']
  ] as [string, string][]).forEach(([a, b]) =>
    links.push({ source: a, target: b })
  );

  return { nodes, links };
}

/* ---------- component ---------- */
export default function SkillsGraph() {
  const { width: winW = 800 } = useWindowSize();
  const fgRef = useRef<ForceGraphMethods<Node, Link>>(null);
  const scrollDataRef = useRef({ lastScrollY: 0, lastTime: Date.now() });
  const animationFrameRef = useRef<number | null>(null);
  const ambientMotionRef = useRef<number | null>(null);
  const ambientTimeRef = useRef(0);

  const data = useMemo(buildGraph, []);

  // Fixed container dimensions
  const containerWidth = Math.min(winW || 800, 800);
  const containerHeight = 600;
  const margin = 80; // Boundary margin

  // Physics controls state with precise optimized values
  const initialControls: PhysicsControls = {
    nodeSizeMultiplier: 1.2, // Will be auto-reduced to 0.9-1.0 after initial render
    repulsionStrength: 500,  // Used with inverse square law for realistic physics
    categoryLinkDistance: 150.00, // Precise category link distance
    skillLinkDistance: 100.00,     // Precise skill link distance
    categoryLinkStrength: 2.00,    // Precise category link strength
    skillLinkStrength: 3.50,       // Precise skill link strength
    boundaryStrength: 0.4,
    gravityStrength: 1.0,
    massMultiplier: 2.5,
    cohesionStrength: 0.15
  };

  const [controls, setControls] = useState<PhysicsControls>(initialControls);
  const [showControls, setShowControls] = useState(false);
  const autoSizeAdjusted = useRef(false);

  // Debounced controls to prevent excessive recalculations - start with same values
  const [debouncedControls, setDebouncedControls] = useState<PhysicsControls>(initialControls);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounce control changes to prevent physics instability
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedControls(controls);
    }, 150); // 150ms debounce

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [controls]);

  /* automatic node size adjustment after initial render */
  useEffect(() => {
    if (autoSizeAdjusted.current) return;

    const adjustNodeSize = () => {
      if (!autoSizeAdjusted.current) {
        console.log('Auto-adjusting node size from 1.2 to 0.95 for more compact layout');
        setControls(prev => ({ ...prev, nodeSizeMultiplier: 0.95 }));
        autoSizeAdjusted.current = true;
      }
    };

    // Delay the adjustment to allow initial physics to settle
    const adjustmentTimeout = setTimeout(adjustNodeSize, 2500); // 2.5 seconds delay

    return () => {
      clearTimeout(adjustmentTimeout);
    };
  }, []);

  /* functional physics setup with working control sliders */
  useEffect(() => {
    const fg = fgRef.current;
    if (!fg) return;

    const baseRadius = (n: Node) => isCat(n.id) ? 85 : 65;

    console.log('Physics update triggered with controls:', debouncedControls);

    // Access the simulation directly through the force graph engine
    const engine = (fg as any).d3Force;
    if (!engine) return;

    // Enhanced repulsion force with inverse square law
    const chargeForce = engine('charge');
    if (chargeForce) {
      // Apply inverse square law with stability factor
      const baseStrength = -debouncedControls.repulsionStrength;
      const stabilityFactor = 0.15; // Conservative scaling for stability
      const inverseSquareStrength = baseStrength * stabilityFactor;

      chargeForce
        .strength(inverseSquareStrength)
        .distanceMax(800)
        .distanceMin(15); // Prevent extreme forces at very close distances

      console.log('Updated inverse square repulsion force strength:', inverseSquareStrength);
    }

    // Update link forces
    const linkForce = engine('link');
    if (linkForce) {
      linkForce
        .distance((l: any) => {
          const distance = isCat((l.source as Node).id) ? debouncedControls.categoryLinkDistance : debouncedControls.skillLinkDistance;
          return distance;
        })
        .strength((l: any) => {
          const strength = isCat((l.source as Node).id) ? debouncedControls.categoryLinkStrength : debouncedControls.skillLinkStrength;
          return strength;
        });
      console.log('Updated link distances - Category:', debouncedControls.categoryLinkDistance, 'Skill:', debouncedControls.skillLinkDistance);
    }

    // Update collision force
    const collideForce = engine('collide');
    if (collideForce) {
      collideForce.radius((n: Node) => baseRadius(n) * debouncedControls.nodeSizeMultiplier).strength(0.8);
      console.log('Updated collision radius multiplier:', debouncedControls.nodeSizeMultiplier);
    }

    // Update center forces
    const centerXForce = engine('centerX');
    if (centerXForce) {
      centerXForce.x(containerWidth / 2).strength(0.01);
    }

    const centerYForce = engine('centerY');
    if (centerYForce) {
      centerYForce.y(containerHeight / 2).strength(0.01);
    }

    // Update connection-based cohesion force
    const connectionCohesionForce = engine('connectionCohesion');
    if (connectionCohesionForce) {
      // Update the cohesion strength parameter
      (connectionCohesionForce as any).strength = debouncedControls.cohesionStrength;
      console.log('Updated cohesion strength:', debouncedControls.cohesionStrength);
    }

    // Update boundary force strength
    const boundaryForce = engine('boundary');
    if (boundaryForce) {
      (boundaryForce as any).strength = debouncedControls.boundaryStrength;
      console.log('Updated boundary strength:', debouncedControls.boundaryStrength);
    }

    // Add custom inverse square law repulsion force
    const customRepulsionForce = engine('customRepulsion');
    if (!customRepulsionForce) {
      engine('customRepulsion', (() => {
        data.nodes.forEach((nodeA: any, i: number) => {
          if (nodeA.x == null || nodeA.y == null) return;

          data.nodes.forEach((nodeB: any, j: number) => {
            if (i >= j || nodeB.x == null || nodeB.y == null) return;

            const dx = nodeB.x - nodeA.x;
            const dy = nodeB.y - nodeA.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 0 && distance < 200) { // Only apply at close range
              // Inverse square law: force ∝ 1/distance²
              const minDistance = 20; // Prevent extreme forces
              const effectiveDistance = Math.max(distance, minDistance);
              const forceStrength = (debouncedControls.repulsionStrength * 0.1) / (effectiveDistance * effectiveDistance);

              const fx = (dx / distance) * forceStrength;
              const fy = (dy / distance) * forceStrength;

              // Apply repulsive forces
              nodeA.vx = (nodeA.vx || 0) - fx;
              nodeA.vy = (nodeA.vy || 0) - fy;
              nodeB.vx = (nodeB.vx || 0) + fx;
              nodeB.vy = (nodeB.vy || 0) + fy;
            }
          });
        });
      }) as any);
    }

    // Restart simulation to apply changes
    fg.d3ReheatSimulation();
    console.log('Physics forces updated and simulation reheated');
  }, [data.nodes, data.links, containerWidth, containerHeight, debouncedControls]);

  /* continuous ambient motion for visual liveliness */
  useEffect(() => {
    const applyAmbientMotion = () => {
      const fg = fgRef.current;
      if (!fg) return;

      const sim = fg.d3Force('simulation');
      if (!sim) return;

      ambientTimeRef.current += 0.02; // Slow time progression

      data.nodes.forEach((node: any, index: number) => {
        if (node.x == null || node.y == null) return;

        // Create unique phase offsets for each node for varied motion
        const phaseX = index * 0.5;
        const phaseY = index * 0.7;

        // Generate smooth, slow sine wave motion
        const motionX = Math.sin(ambientTimeRef.current + phaseX) * 0.3; // Very small amplitude
        const motionY = Math.cos(ambientTimeRef.current * 0.8 + phaseY) * 0.3;

        // Apply gentle ambient forces
        const ambientStrength = 0.001; // Extremely subtle
        node.vx = (node.vx || 0) + motionX * ambientStrength;
        node.vy = (node.vy || 0) + motionY * ambientStrength;

        // Clamp ambient motion to prevent accumulation
        const maxAmbientVelocity = 0.5;
        if (Math.abs(node.vx || 0) > maxAmbientVelocity) {
          node.vx = Math.sign(node.vx || 0) * maxAmbientVelocity;
        }
        if (Math.abs(node.vy || 0) > maxAmbientVelocity) {
          node.vy = Math.sign(node.vy || 0) * maxAmbientVelocity;
        }
      });

      // Keep simulation slightly warm for ambient motion
      const currentAlpha = sim.alpha() || 0;
      if (currentAlpha < 0.01) {
        sim.alpha(0.01);
      }
    };

    const startAmbientMotion = () => {
      if (ambientMotionRef.current) return;

      const animate = () => {
        applyAmbientMotion();
        ambientMotionRef.current = requestAnimationFrame(animate);
      };

      ambientMotionRef.current = requestAnimationFrame(animate);
    };

    // Start ambient motion after a short delay to let initial physics settle
    const startTimeout = setTimeout(startAmbientMotion, 2000);

    return () => {
      clearTimeout(startTimeout);
      if (ambientMotionRef.current) {
        cancelAnimationFrame(ambientMotionRef.current);
        ambientMotionRef.current = null;
      }
    };
  }, [data.nodes]);

  /* enhanced scroll-responsive gravity physics */
  useEffect(() => {
    let isScrolling = false;

    const handleScroll = () => {
      if (isScrolling) return; // Prevent multiple simultaneous calls
      isScrolling = true;

      requestAnimationFrame(() => {
        const currentTime = Date.now();
        const currentScrollY = window.scrollY;
        const deltaTime = currentTime - scrollDataRef.current.lastTime;
        const deltaScroll = currentScrollY - scrollDataRef.current.lastScrollY;

        if (deltaTime > 0) { // Process all scroll events
          const scrollVelocity = deltaScroll / deltaTime; // pixels per ms

          const fg = fgRef.current;
          if (fg && Math.abs(scrollVelocity) > 0.001) { // Very low threshold for responsiveness
            const scrollSim = fg.d3Force('simulation');

            data.nodes.forEach((node: any) => {
              if (node.x == null || node.y == null) return;

              // INVERTED MASS EFFECT: Shorter text = stronger gravity, longer text = weaker gravity
              const textLength = node.id.length;
              const baseMass = 10; // Base mass to prevent division by zero
              const inverseMass = baseMass / (textLength + 1); // Inverse relationship

              // Velocity-based gravity with smooth easing
              const velocityMagnitude = Math.abs(scrollVelocity);
              const velocityEasing = Math.min(velocityMagnitude * 100, 1); // Smooth easing curve

              // Enhanced gravity formula: baseGravity * scrollVelocity / textLength
              const baseGravity = debouncedControls.gravityStrength * 10;
              const gravityEffect = baseGravity * scrollVelocity * inverseMass * velocityEasing;

              // Apply smooth momentum with natural physics feel
              const momentum = gravityEffect * 0.8; // Slightly damped for natural feel
              const previousVelocity = node.vy || 0;
              const newVelocity = previousVelocity * 0.9 + momentum; // Blend with previous velocity

              node.vy = newVelocity;

              // Dynamic velocity limits based on scroll intensity
              const dynamicMaxVelocity = Math.min(30, velocityMagnitude * 1000);
              node.vy = Math.max(-dynamicMaxVelocity, Math.min(dynamicMaxVelocity, node.vy));

              // Smooth boundary constraints with easing
              const nodeRadius = (isCat(node.id) ? 85 : 65) * debouncedControls.nodeSizeMultiplier;
              const boundaryEasing = 0.8;

              if (node.y < margin + nodeRadius && node.vy < 0) {
                node.vy = node.vy * boundaryEasing; // Smooth deceleration at top
              }
              if (node.y > containerHeight - margin - nodeRadius && node.vy > 0) {
                node.vy = node.vy * boundaryEasing; // Smooth deceleration at bottom
              }

              // Debug logging for mass effect verification
              if (Math.abs(scrollVelocity) > 0.01) {
                console.log(`Node "${node.id}" (length: ${textLength}): inverseMass=${inverseMass.toFixed(3)}, gravity=${gravityEffect.toFixed(3)}`);
              }
            });

            // Always restart simulation when scroll occurs for immediate response
            if (scrollSim) {
              scrollSim.alpha(Math.max(0.1, (scrollSim.alpha() || 0) + 0.05)).restart();
            }

            console.log(`Scroll gravity applied: velocity=${scrollVelocity.toFixed(4)}, direction=${scrollVelocity > 0 ? 'down' : 'up'}`);
          }
        }

        scrollDataRef.current.lastScrollY = currentScrollY;
        scrollDataRef.current.lastTime = currentTime;
        isScrolling = false;
      });
    };

    // Initialize scroll data
    scrollDataRef.current = { lastScrollY: window.scrollY, lastTime: Date.now() };

    // Add scroll listener to both window and document for better coverage
    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('scroll', handleScroll, { passive: true });

    console.log('Scroll gravity physics initialized');

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('scroll', handleScroll);
    };
  }, [data.nodes, debouncedControls, containerHeight, margin]);

  /* pill renderer with dynamic sizing */
  const draw = useCallback((
    n: Node,
    ctx: CanvasRenderingContext2D,
    s: number,
    mask?: string
  ) => {
    if (n.x == null || n.y == null) return;      // not yet positioned

    const sizeMultiplier = debouncedControls.nodeSizeMultiplier;
    const font = (18 * sizeMultiplier) / s;
    const padX = (12 * sizeMultiplier) / s;
    const padY = (6 * sizeMultiplier) / s;

    ctx.font = `600 ${font}px Inter, sans-serif`;
    const w = ctx.measureText(n.id).width + padX * 2;
    const h = font + padY * 2;
    const x = n.x - w / 2, y = n.y - h / 2, r = (9 * sizeMultiplier) / s;

    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.arcTo(x + w, y,     x + w, y + h, r);
    ctx.arcTo(x + w, y + h, x,     y + h, r);
    ctx.arcTo(x,     y + h, x,     y,     r);
    ctx.arcTo(x,     y,     x + w, y,     r);
    ctx.closePath();

    if (mask) { ctx.fillStyle = mask; ctx.fill(); return; }

    const col = colorOf[n.group as Cat];
    ctx.fillStyle   = `${col}22`;
    ctx.strokeStyle = col;
    ctx.lineWidth   = (1 * sizeMultiplier) / s;
    ctx.fill(); ctx.stroke();

    ctx.fillStyle = col;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(n.id, n.x, n.y);
  }, [debouncedControls.nodeSizeMultiplier]);

  // Control slider component
  const ControlSlider = ({ label, value, min, max, step, onChange }: {
    label: string;
    value: number;
    min: number;
    max: number;
    step: number;
    onChange: (value: number) => void;
  }) => (
    <div className="mb-3">
      <label className="block text-xs text-gray-400 mb-1">{label}: {value.toFixed(2)}</label>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
      />
    </div>
  );

  return (
    <div className="mx-auto relative" style={{ width: containerWidth }}>
      {/* Visual boundary container */}
      <div
        className="absolute inset-0 border border-white/10 rounded-lg pointer-events-none"
        style={{ width: containerWidth, height: containerHeight }}
      />

      {/* Physics Controls Panel */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={() => setShowControls(!showControls)}
          className="bg-gray-800 text-white px-3 py-2 rounded-lg text-sm border border-gray-600 hover:bg-gray-700"
        >
          {showControls ? 'Hide' : 'Show'} Controls
        </button>

        {showControls && (
          <div className="mt-2 bg-gray-900 border border-gray-600 rounded-lg p-4 w-64 max-h-96 overflow-y-auto">
            <h3 className="text-white text-sm font-semibold mb-3">Physics Controls</h3>

            <ControlSlider
              label="Node Size"
              value={controls.nodeSizeMultiplier}
              min={0.5}
              max={2.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, nodeSizeMultiplier: value }))}
            />

            <ControlSlider
              label="Repulsion Force"
              value={controls.repulsionStrength}
              min={50}
              max={1000}
              step={50}
              onChange={(value) => setControls(prev => ({ ...prev, repulsionStrength: value }))}
            />

            <ControlSlider
              label="Category Link Distance"
              value={controls.categoryLinkDistance}
              min={200}
              max={600}
              step={25}
              onChange={(value) => setControls(prev => ({ ...prev, categoryLinkDistance: value }))}
            />

            <ControlSlider
              label="Skill Link Distance"
              value={controls.skillLinkDistance}
              min={200}
              max={600}
              step={25}
              onChange={(value) => setControls(prev => ({ ...prev, skillLinkDistance: value }))}
            />

            <ControlSlider
              label="Category Link Strength"
              value={controls.categoryLinkStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, categoryLinkStrength: value }))}
            />

            <ControlSlider
              label="Skill Link Strength"
              value={controls.skillLinkStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, skillLinkStrength: value }))}
            />

            <ControlSlider
              label="Boundary Repulsion"
              value={controls.boundaryStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, boundaryStrength: value }))}
            />

            <ControlSlider
              label="Gravity Sensitivity"
              value={controls.gravityStrength}
              min={0.1}
              max={2.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, gravityStrength: value }))}
            />

            <ControlSlider
              label="Mass Multiplier"
              value={controls.massMultiplier}
              min={0.5}
              max={5.0}
              step={0.5}
              onChange={(value) => setControls(prev => ({ ...prev, massMultiplier: value }))}
            />

            <ControlSlider
              label="Cohesion Strength"
              value={controls.cohesionStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, cohesionStrength: value }))}
            />
          </div>
        )}
      </div>

      <ForceGraph2D
        ref={fgRef as any}
        width={containerWidth}
        height={containerHeight}
        backgroundColor="rgba(0,0,0,0)"
        graphData={data}
        nodeCanvasObject={(n, ctx, s) => draw(n as Node, ctx, s)}
        nodePointerAreaPaint={(n, c, ctx, s) => draw(n as Node, ctx, s, c)}
        linkColor={() => '#334155'}
        linkWidth={0.6}

        // Enable selective interactions
        enableNodeDrag={true}
        enablePanInteraction={false}
        enableZoomInteraction={false}

        // Stable node drag handlers
        onNodeDrag={(node: any) => {
          node.fx = node.x;
          node.fy = node.y;
          // Don't reheat during drag - let the simulation continue naturally
        }}
        onNodeDragEnd={(node: any) => {
          node.fx = undefined;
          node.fy = undefined;
          // Gentle restart instead of full reheat
          const dragSim = fgRef.current?.d3Force('simulation');
          if (dragSim) {
            dragSim.alpha(Math.min(0.1, (dragSim.alpha() || 0) + 0.05)).restart();
          }
        }}
      />
    </div>
  );
}
