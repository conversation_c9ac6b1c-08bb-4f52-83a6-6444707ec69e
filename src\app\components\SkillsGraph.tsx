/* SkillsGraph.tsx – Enhanced 2-D physics-based skills graph with scroll gravity
   ---------------------------------------------------------------------------- */
'use client';

import dynamic from 'next/dynamic';
import { useWindowSize } from '@uidotdev/usehooks';
import { useEffect, useMemo, useRef, useCallback, useState } from 'react';
import type { ForceGraphMethods } from 'react-force-graph-2d';
import * as d3 from 'd3-force';

/* ---------- lazy component ---------- */
const ForceGraph2D = dynamic(
  () => import('react-force-graph-2d').then(m => m.default),
  { ssr: false }
);

/* ---------- types ---------- */
type Node = {
  id: string;
  group: string;
  fx?: number;
  fy?: number;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
};
type Link = { source: string; target: string };

interface PhysicsControls {
  nodeSizeMultiplier: number;
  repulsionStrength: number;
  categoryLinkDistance: number;
  skillLinkDistance: number;
  categoryLinkStrength: number;
  skillLinkStrength: number;
  boundaryStrength: number;
  gravityStrength: number;
  massMultiplier: number;
  cohesionStrength: number;
}

const cats = [
  'Programming Languages',
  'Frameworks & Tech',
  'Machine Learning & AI',
  'Cloud & DevOps'
] as const;
type Cat = typeof cats[number];
const isCat = (id: string): id is Cat => (cats as readonly string[]).includes(id);

const skills: Record<Cat, string[]> = {
  'Programming Languages': ['Python (primary)', 'Java', 'C++'],
  'Frameworks & Tech': [
    'Django', 'Node.js', 'FastAPI', 'React.js', 'Angular',
    'Next.js', 'Tailwind CSS', 'TypeScript', 'MongoDB',
    'MySQL', 'SQLite3'
  ],
  'Machine Learning & AI': [
    'TensorFlow', 'PyTorch', 'Scikit-learn', 'Transformers',
    'CNNs', 'RNNs', 'OpenAI API', 'Hugging Face'
  ],
  'Cloud & DevOps': [
    'AWS (EC2, S3, Lambda)', 'Docker', 'Kubernetes',
    'CI/CD (Jenkins, GitHub Actions)'
  ]
};

const colorOf: Record<Cat, string> = {
  'Programming Languages': '#3b82f6',
  'Frameworks & Tech':     '#06b6d4',
  'Machine Learning & AI': '#d946ef',
  'Cloud & DevOps':        '#ea580c'
};

function buildGraph() {
  const nodes: Node[] = [];
  const links: Link[] = [];

  cats.forEach(cat => {
    nodes.push({ id: cat, group: cat });
    skills[cat].forEach(skill => {
      nodes.push({ id: skill, group: cat });
      links.push({ source: cat, target: skill });
    });
  });

  cats.forEach(cat => {
    const list = skills[cat];
    list.forEach((skill, i) =>
      links.push({ source: skill, target: list[(i + 1) % list.length] })
    );
  });

  ([
    ['Python (primary)', 'Django'], ['Python (primary)', 'FastAPI'],
    ['Python (primary)', 'TensorFlow'], ['Python (primary)', 'PyTorch'],
    ['Python (primary)', 'Scikit-learn'], ['Java', 'MySQL'],
    ['Java', 'AWS (EC2, S3, Lambda)'], ['Node.js', 'TypeScript'],
    ['Node.js', 'MongoDB'], ['Node.js', 'Docker'],
    ['React.js', 'Next.js'], ['Docker', 'Kubernetes'],
    ['Docker', 'CI/CD (Jenkins, GitHub Actions)'],
    ['Kubernetes', 'AWS (EC2, S3, Lambda)'],
    ['TensorFlow', 'CNNs'], ['PyTorch', 'Transformers'],
    ['OpenAI API', 'Transformers'], ['MongoDB', 'AWS (EC2, S3, Lambda)'],
    ['MySQL', 'CI/CD (Jenkins, GitHub Actions)']
  ] as [string, string][]).forEach(([a, b]) =>
    links.push({ source: a, target: b })
  );

  return { nodes, links };
}

/* ---------- component ---------- */
export default function SkillsGraph() {
  const { width: winW = 800 } = useWindowSize();
  const fgRef = useRef<ForceGraphMethods<Node, Link>>(null);
  const scrollDataRef = useRef({ lastScrollY: 0, lastTime: Date.now() });
  const animationFrameRef = useRef<number | null>(null);

  const data = useMemo(buildGraph, []);

  // Fixed container dimensions
  const containerWidth = Math.min(winW || 800, 800);
  const containerHeight = 600;
  const margin = 80; // Boundary margin

  // Physics controls state
  const [controls, setControls] = useState<PhysicsControls>({
    nodeSizeMultiplier: 1.0,
    repulsionStrength: 400,
    categoryLinkDistance: 450,
    skillLinkDistance: 400,
    categoryLinkStrength: 0.4,
    skillLinkStrength: 0.2,
    boundaryStrength: 0.3,
    gravityStrength: 0.5,
    massMultiplier: 2.0,
    cohesionStrength: 0.2
  });

  const [showControls, setShowControls] = useState(false);

  /* physics setup with enhanced forces */
  useEffect(() => {
    const fg = fgRef.current;
    if (!fg) return;

    // Repulsion force between nodes
    fg.d3Force('charge')!.strength(-controls.repulsionStrength).distanceMax(800);

    // Link forces for connections
    fg.d3Force('link')!
      .distance((l: any) => (isCat((l.source as Node).id) ? controls.categoryLinkDistance : controls.skillLinkDistance))
      .strength((l: any) => (isCat((l.source as Node).id) ? controls.categoryLinkStrength : controls.skillLinkStrength));

    // Collision detection to prevent overlapping
    const baseRadius = (n: Node) => isCat(n.id) ? 85 : 65;
    fg.d3Force('collide', d3.forceCollide<Node>().radius(n => baseRadius(n) * controls.nodeSizeMultiplier).strength(0.8) as any);

    // Connection-based cohesion forces (replace global center force)
    fg.d3Force('cohesion', (() => {
      data.links.forEach((link: any) => {
        const source = typeof link.source === 'object' ? link.source : data.nodes.find(n => n.id === link.source);
        const target = typeof link.target === 'object' ? link.target : data.nodes.find(n => n.id === link.target);

        if (source && target && source.x != null && source.y != null && target.x != null && target.y != null) {
          const dx = target.x - source.x;
          const dy = target.y - source.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance > 0) {
            const force = controls.cohesionStrength * 0.1;
            const fx = (dx / distance) * force;
            const fy = (dy / distance) * force;

            source.vx = (source.vx || 0) + fx;
            source.vy = (source.vy || 0) + fy;
            target.vx = (target.vx || 0) - fx;
            target.vy = (target.vy || 0) - fy;
          }
        }
      });
    }) as any);

    // Boundary forces - repel nodes from edges
    fg.d3Force('boundary', (() => {
      data.nodes.forEach((node: any) => {
        if (node.x == null || node.y == null) return;

        const nodeRadius = baseRadius(node) * controls.nodeSizeMultiplier;

        // Left boundary
        if (node.x < margin + nodeRadius) {
          node.vx = (node.vx || 0) + controls.boundaryStrength * (margin + nodeRadius - node.x);
        }
        // Right boundary
        if (node.x > containerWidth - margin - nodeRadius) {
          node.vx = (node.vx || 0) - controls.boundaryStrength * (node.x - (containerWidth - margin - nodeRadius));
        }
        // Top boundary
        if (node.y < margin + nodeRadius) {
          node.vy = (node.vy || 0) + controls.boundaryStrength * (margin + nodeRadius - node.y);
        }
        // Bottom boundary
        if (node.y > containerHeight - margin - nodeRadius) {
          node.vy = (node.vy || 0) - controls.boundaryStrength * (node.y - (containerHeight - margin - nodeRadius));
        }
      });
    }) as any);

    // Restart simulation with new forces
    fg.d3ReheatSimulation();
  }, [data.nodes, data.links, containerWidth, containerHeight, controls]);

  /* scroll-responsive gravity physics */
  useEffect(() => {
    const handleScroll = () => {
      if (animationFrameRef.current) return; // Throttle to 60fps

      animationFrameRef.current = requestAnimationFrame(() => {
        const currentTime = Date.now();
        const currentScrollY = window.scrollY;
        const deltaTime = currentTime - scrollDataRef.current.lastTime;
        const deltaScroll = currentScrollY - scrollDataRef.current.lastScrollY;

        if (deltaTime > 0) {
          const scrollVelocity = deltaScroll / deltaTime; // pixels per ms

          const fg = fgRef.current;
          if (fg && Math.abs(scrollVelocity) > 0.01) { // Only apply if significant scroll
            data.nodes.forEach((node: any) => {
              if (node.x == null || node.y == null) return;

              // Calculate mass based on text length
              const mass = node.id.length * controls.massMultiplier;
              const gravityForce = scrollVelocity * mass * controls.gravityStrength * 0.1;

              // Apply gravity force (positive = down, negative = up)
              node.vy = (node.vy || 0) + gravityForce;

              // Ensure nodes don't fall out of boundaries due to gravity
              const nodeRadius = (isCat(node.id) ? 85 : 65) * controls.nodeSizeMultiplier;
              if (node.y + gravityForce < margin + nodeRadius) {
                node.vy = Math.max(node.vy || 0, 0); // Stop upward movement at top
              }
              if (node.y + gravityForce > containerHeight - margin - nodeRadius) {
                node.vy = Math.min(node.vy || 0, 0); // Stop downward movement at bottom
              }
            });

            fg.d3ReheatSimulation();
          }
        }

        scrollDataRef.current.lastScrollY = currentScrollY;
        scrollDataRef.current.lastTime = currentTime;
        animationFrameRef.current = null;
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [data.nodes, controls, containerHeight, margin]);

  /* pill renderer with dynamic sizing */
  const draw = useCallback((
    n: Node,
    ctx: CanvasRenderingContext2D,
    s: number,
    mask?: string
  ) => {
    if (n.x == null || n.y == null) return;      // not yet positioned

    const sizeMultiplier = controls.nodeSizeMultiplier;
    const font = (18 * sizeMultiplier) / s;
    const padX = (12 * sizeMultiplier) / s;
    const padY = (6 * sizeMultiplier) / s;

    ctx.font = `600 ${font}px Inter, sans-serif`;
    const w = ctx.measureText(n.id).width + padX * 2;
    const h = font + padY * 2;
    const x = n.x - w / 2, y = n.y - h / 2, r = (9 * sizeMultiplier) / s;

    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.arcTo(x + w, y,     x + w, y + h, r);
    ctx.arcTo(x + w, y + h, x,     y + h, r);
    ctx.arcTo(x,     y + h, x,     y,     r);
    ctx.arcTo(x,     y,     x + w, y,     r);
    ctx.closePath();

    if (mask) { ctx.fillStyle = mask; ctx.fill(); return; }

    const col = colorOf[n.group as Cat];
    ctx.fillStyle   = `${col}22`;
    ctx.strokeStyle = col;
    ctx.lineWidth   = (1 * sizeMultiplier) / s;
    ctx.fill(); ctx.stroke();

    ctx.fillStyle = col;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(n.id, n.x, n.y);
  }, [controls.nodeSizeMultiplier]);

  // Control slider component
  const ControlSlider = ({ label, value, min, max, step, onChange }: {
    label: string;
    value: number;
    min: number;
    max: number;
    step: number;
    onChange: (value: number) => void;
  }) => (
    <div className="mb-3">
      <label className="block text-xs text-gray-400 mb-1">{label}: {value.toFixed(2)}</label>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
      />
    </div>
  );

  return (
    <div className="mx-auto relative" style={{ width: containerWidth }}>
      {/* Visual boundary container */}
      <div
        className="absolute inset-0 border border-white/10 rounded-lg pointer-events-none"
        style={{ width: containerWidth, height: containerHeight }}
      />

      {/* Physics Controls Panel */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={() => setShowControls(!showControls)}
          className="bg-gray-800 text-white px-3 py-2 rounded-lg text-sm border border-gray-600 hover:bg-gray-700"
        >
          {showControls ? 'Hide' : 'Show'} Controls
        </button>

        {showControls && (
          <div className="mt-2 bg-gray-900 border border-gray-600 rounded-lg p-4 w-64 max-h-96 overflow-y-auto">
            <h3 className="text-white text-sm font-semibold mb-3">Physics Controls</h3>

            <ControlSlider
              label="Node Size"
              value={controls.nodeSizeMultiplier}
              min={0.5}
              max={2.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, nodeSizeMultiplier: value }))}
            />

            <ControlSlider
              label="Repulsion Force"
              value={controls.repulsionStrength}
              min={100}
              max={1000}
              step={50}
              onChange={(value) => setControls(prev => ({ ...prev, repulsionStrength: value }))}
            />

            <ControlSlider
              label="Category Link Distance"
              value={controls.categoryLinkDistance}
              min={200}
              max={600}
              step={25}
              onChange={(value) => setControls(prev => ({ ...prev, categoryLinkDistance: value }))}
            />

            <ControlSlider
              label="Skill Link Distance"
              value={controls.skillLinkDistance}
              min={200}
              max={600}
              step={25}
              onChange={(value) => setControls(prev => ({ ...prev, skillLinkDistance: value }))}
            />

            <ControlSlider
              label="Category Link Strength"
              value={controls.categoryLinkStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, categoryLinkStrength: value }))}
            />

            <ControlSlider
              label="Skill Link Strength"
              value={controls.skillLinkStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, skillLinkStrength: value }))}
            />

            <ControlSlider
              label="Boundary Repulsion"
              value={controls.boundaryStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, boundaryStrength: value }))}
            />

            <ControlSlider
              label="Gravity Sensitivity"
              value={controls.gravityStrength}
              min={0.1}
              max={2.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, gravityStrength: value }))}
            />

            <ControlSlider
              label="Mass Multiplier"
              value={controls.massMultiplier}
              min={0.5}
              max={5.0}
              step={0.5}
              onChange={(value) => setControls(prev => ({ ...prev, massMultiplier: value }))}
            />

            <ControlSlider
              label="Cohesion Strength"
              value={controls.cohesionStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, cohesionStrength: value }))}
            />
          </div>
        )}
      </div>

      <ForceGraph2D
        ref={fgRef as any}
        width={containerWidth}
        height={containerHeight}
        backgroundColor="rgba(0,0,0,0)"
        graphData={data}
        nodeCanvasObject={(n, ctx, s) => draw(n as Node, ctx, s)}
        nodePointerAreaPaint={(n, c, ctx, s) => draw(n as Node, ctx, s, c)}
        linkColor={() => '#334155'}
        linkWidth={0.6}

        // Enable selective interactions
        enableNodeDrag={true}
        enablePanInteraction={false}
        enableZoomInteraction={false}

        // Node drag handlers
        onNodeDrag={(node: any) => {
          node.fx = node.x;
          node.fy = node.y;
          fgRef.current?.d3ReheatSimulation();
        }}
        onNodeDragEnd={(node: any) => {
          node.fx = undefined;
          node.fy = undefined;
          fgRef.current?.d3ReheatSimulation();
        }}
      />
    </div>
  );
}
