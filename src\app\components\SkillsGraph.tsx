/* SkillsGraph.tsx – Enhanced 2-D physics-based skills graph with scroll gravity
   ---------------------------------------------------------------------------- */
'use client';

import dynamic from 'next/dynamic';
import { useWindowSize } from '@uidotdev/usehooks';
import { useEffect, useMemo, useRef, useCallback, useState } from 'react';
import type { ForceGraphMethods } from 'react-force-graph-2d';
import * as d3 from 'd3-force';

/* ---------- lazy component ---------- */
const ForceGraph2D = dynamic(
  () => import('react-force-graph-2d').then(m => m.default),
  { ssr: false }
);

/* ---------- types ---------- */
type Node = {
  id: string;
  group: string;
  fx?: number;
  fy?: number;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
};
type Link = { source: string; target: string };

interface PhysicsControls {
  nodeSizeMultiplier: number;
  repulsionStrength: number;
  categoryLinkDistance: number;
  skillLinkDistance: number;
  categoryLinkStrength: number;
  skillLinkStrength: number;
  boundaryStrength: number;
  gravityStrength: number;
  massMultiplier: number;
  cohesionStrength: number;
}

const cats = [
  'Programming Languages',
  'Frameworks & Tech',
  'Machine Learning & AI',
  'Cloud & DevOps'
] as const;
type Cat = typeof cats[number];
const isCat = (id: string): id is Cat => (cats as readonly string[]).includes(id);

const skills: Record<Cat, string[]> = {
  'Programming Languages': ['Python (primary)', 'Java', 'C++'],
  'Frameworks & Tech': [
    'Django', 'Node.js', 'FastAPI', 'React.js', 'Angular',
    'Next.js', 'Tailwind CSS', 'TypeScript', 'MongoDB',
    'MySQL', 'SQLite3'
  ],
  'Machine Learning & AI': [
    'TensorFlow', 'PyTorch', 'Scikit-learn', 'Transformers',
    'CNNs', 'RNNs', 'OpenAI API', 'Hugging Face'
  ],
  'Cloud & DevOps': [
    'AWS (EC2, S3, Lambda)', 'Docker', 'Kubernetes',
    'CI/CD (Jenkins, GitHub Actions)'
  ]
};

const colorOf: Record<Cat, string> = {
  'Programming Languages': '#3b82f6',
  'Frameworks & Tech':     '#06b6d4',
  'Machine Learning & AI': '#d946ef',
  'Cloud & DevOps':        '#ea580c'
};

function buildGraph() {
  const nodes: Node[] = [];
  const links: Link[] = [];

  cats.forEach(cat => {
    nodes.push({ id: cat, group: cat });
    skills[cat].forEach(skill => {
      nodes.push({ id: skill, group: cat });
      links.push({ source: cat, target: skill });
    });
  });

  cats.forEach(cat => {
    const list = skills[cat];
    list.forEach((skill, i) =>
      links.push({ source: skill, target: list[(i + 1) % list.length] })
    );
  });

  ([
    ['Python (primary)', 'Django'], ['Python (primary)', 'FastAPI'],
    ['Python (primary)', 'TensorFlow'], ['Python (primary)', 'PyTorch'],
    ['Python (primary)', 'Scikit-learn'], ['Java', 'MySQL'],
    ['Java', 'AWS (EC2, S3, Lambda)'], ['Node.js', 'TypeScript'],
    ['Node.js', 'MongoDB'], ['Node.js', 'Docker'],
    ['React.js', 'Next.js'], ['Docker', 'Kubernetes'],
    ['Docker', 'CI/CD (Jenkins, GitHub Actions)'],
    ['Kubernetes', 'AWS (EC2, S3, Lambda)'],
    ['TensorFlow', 'CNNs'], ['PyTorch', 'Transformers'],
    ['OpenAI API', 'Transformers'], ['MongoDB', 'AWS (EC2, S3, Lambda)'],
    ['MySQL', 'CI/CD (Jenkins, GitHub Actions)']
  ] as [string, string][]).forEach(([a, b]) =>
    links.push({ source: a, target: b })
  );

  return { nodes, links };
}

/* ---------- component ---------- */
export default function SkillsGraph() {
  const { width: winW = 800 } = useWindowSize();
  const fgRef = useRef<ForceGraphMethods<Node, Link>>(null);
  const scrollDataRef = useRef({ lastScrollY: 0, lastTime: Date.now() });
  const animationFrameRef = useRef<number | null>(null);

  const data = useMemo(buildGraph, []);

  // Fixed container dimensions
  const containerWidth = Math.min(winW || 800, 800);
  const containerHeight = 600;
  const margin = 80; // Boundary margin

  // Physics controls state with optimal initial values for good spacing
  const initialControls: PhysicsControls = {
    nodeSizeMultiplier: 1.2, // Start with slightly larger nodes for better spacing
    repulsionStrength: 500,  // Higher repulsion for better spread
    categoryLinkDistance: 480,
    skillLinkDistance: 420,
    categoryLinkStrength: 0.3,
    skillLinkStrength: 0.15,
    boundaryStrength: 0.4,
    gravityStrength: 1.0,
    massMultiplier: 2.5,
    cohesionStrength: 0.15
  };

  const [controls, setControls] = useState<PhysicsControls>(initialControls);
  const [showControls, setShowControls] = useState(false);

  // Debounced controls to prevent excessive recalculations - start with same values
  const [debouncedControls, setDebouncedControls] = useState<PhysicsControls>(initialControls);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounce control changes to prevent physics instability
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedControls(controls);
    }, 150); // 150ms debounce

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [controls]);

  /* stable physics setup with proper D3 force integration */
  useEffect(() => {
    const fg = fgRef.current;
    if (!fg) return;

    const baseRadius = (n: Node) => isCat(n.id) ? 85 : 65;

    // Get the simulation to access forces properly
    const sim = fg.d3Force('simulation');
    if (!sim) return;

    // Repulsion force between nodes - access the actual force object
    const chargeForce = sim.force('charge');
    if (chargeForce) {
      (chargeForce as any).strength(-debouncedControls.repulsionStrength).distanceMax(800);
    }

    // Link forces for connections - access the actual force object
    const linkForce = sim.force('link');
    if (linkForce) {
      (linkForce as any)
        .distance((l: any) => (isCat((l.source as Node).id) ? debouncedControls.categoryLinkDistance : debouncedControls.skillLinkDistance))
        .strength((l: any) => (isCat((l.source as Node).id) ? debouncedControls.categoryLinkStrength : debouncedControls.skillLinkStrength));
    }

    // Collision detection to prevent overlapping
    const collideForce = sim.force('collide');
    if (collideForce) {
      (collideForce as any).radius((n: Node) => baseRadius(n) * debouncedControls.nodeSizeMultiplier).strength(0.8);
    } else {
      sim.force('collide', d3.forceCollide<Node>().radius(n => baseRadius(n) * debouncedControls.nodeSizeMultiplier).strength(0.8));
    }

    // Center forces for general cohesion
    const centerXForce = sim.force('centerX');
    if (centerXForce) {
      (centerXForce as any).x(containerWidth / 2).strength(0.01);
    } else {
      sim.force('centerX', d3.forceX().x(containerWidth / 2).strength(0.01));
    }

    const centerYForce = sim.force('centerY');
    if (centerYForce) {
      (centerYForce as any).y(containerHeight / 2).strength(0.01);
    } else {
      sim.force('centerY', d3.forceY().y(containerHeight / 2).strength(0.01));
    }

    // Connection-based cohesion force
    const connectionCohesionForce = sim.force('connectionCohesion');
    if (connectionCohesionForce) {
      // Update existing force
      (connectionCohesionForce as any).strength(debouncedControls.cohesionStrength * 0.01);
    } else {
      // Create new connection cohesion force
      sim.force('connectionCohesion', (() => {
        const alpha = sim.alpha() || 0.3;
        const dampening = Math.max(0.1, alpha);

        data.links.forEach((link: any) => {
          const source = typeof link.source === 'object' ? link.source : data.nodes.find(n => n.id === link.source);
          const target = typeof link.target === 'object' ? link.target : data.nodes.find(n => n.id === link.target);

          if (source && target && source.x != null && source.y != null && target.x != null && target.y != null) {
            const dx = target.x - source.x;
            const dy = target.y - source.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 0 && distance > 50) {
              const force = debouncedControls.cohesionStrength * 0.01 * dampening;
              const fx = (dx / distance) * force;
              const fy = (dy / distance) * force;

              source.vx = (source.vx || 0) + fx * 0.5;
              source.vy = (source.vy || 0) + fy * 0.5;
              target.vx = (target.vx || 0) - fx * 0.5;
              target.vy = (target.vy || 0) - fy * 0.5;
            }
          }
        });
      }) as any);
    }

    // Boundary forces
    const boundaryForce = sim.force('boundary');
    if (boundaryForce) {
      // Update existing boundary force
      (boundaryForce as any).strength(debouncedControls.boundaryStrength);
    } else {
      // Create new boundary force
      sim.force('boundary', (() => {
        const alpha = sim.alpha() || 0.3;
        const dampening = Math.max(0.1, alpha);

        data.nodes.forEach((node: any) => {
          if (node.x == null || node.y == null) return;

          const nodeRadius = baseRadius(node) * debouncedControls.nodeSizeMultiplier;
          const boundaryStrength = debouncedControls.boundaryStrength * dampening;

          // Left boundary
          if (node.x < margin + nodeRadius) {
            const force = boundaryStrength * (margin + nodeRadius - node.x) * 0.1;
            node.vx = (node.vx || 0) + force;
          }
          // Right boundary
          if (node.x > containerWidth - margin - nodeRadius) {
            const force = boundaryStrength * (node.x - (containerWidth - margin - nodeRadius)) * 0.1;
            node.vx = (node.vx || 0) - force;
          }
          // Top boundary
          if (node.y < margin + nodeRadius) {
            const force = boundaryStrength * (margin + nodeRadius - node.y) * 0.1;
            node.vy = (node.vy || 0) + force;
          }
          // Bottom boundary
          if (node.y > containerHeight - margin - nodeRadius) {
            const force = boundaryStrength * (node.y - (containerHeight - margin - nodeRadius)) * 0.1;
            node.vy = (node.vy || 0) - force;
          }
        });
      }) as any);
    }

    // Configure simulation parameters for stability
    sim
      .alpha(0.3) // Lower initial energy
      .alphaDecay(0.02) // Slower cooling for stability
      .velocityDecay(0.4) // Higher velocity decay for dampening
      .alphaMin(0.001); // Lower minimum alpha

    // Gentle restart - don't use reheat for control changes
    sim.alpha(0.1).restart();
  }, [data.nodes, data.links, containerWidth, containerHeight, debouncedControls]);

  /* functional scroll-responsive gravity physics */
  useEffect(() => {
    const handleScroll = () => {
      if (animationFrameRef.current) return; // Throttle to 60fps

      animationFrameRef.current = requestAnimationFrame(() => {
        const currentTime = Date.now();
        const currentScrollY = window.scrollY;
        const deltaTime = currentTime - scrollDataRef.current.lastTime;
        const deltaScroll = currentScrollY - scrollDataRef.current.lastScrollY;

        if (deltaTime > 8) { // Process more frequently for responsiveness
          const scrollVelocity = deltaScroll / deltaTime; // pixels per ms

          const fg = fgRef.current;
          if (fg && Math.abs(scrollVelocity) > 0.005) { // Lower threshold for better responsiveness
            const scrollSim = fg.d3Force('simulation');
            const currentAlpha = scrollSim?.alpha() || 0.1;

            data.nodes.forEach((node: any) => {
              if (node.x == null || node.y == null) return;

              // Calculate mass based on text length
              const mass = node.id.length * debouncedControls.massMultiplier;
              const gravityForce = scrollVelocity * mass * debouncedControls.gravityStrength * 0.5; // Increased force

              // Apply gravity force with proper scaling
              const appliedForce = gravityForce * 2.0; // Make it more noticeable
              node.vy = (node.vy || 0) + appliedForce;

              // Clamp velocity to prevent runaway motion but allow more movement
              const maxVelocity = 15;
              node.vy = Math.max(-maxVelocity, Math.min(maxVelocity, node.vy || 0));

              // Ensure nodes don't fall out of boundaries due to gravity
              const nodeRadius = (isCat(node.id) ? 85 : 65) * debouncedControls.nodeSizeMultiplier;
              if (node.y < margin + nodeRadius && node.vy < 0) {
                node.vy = Math.max(node.vy, -2); // Allow some upward movement
              }
              if (node.y > containerHeight - margin - nodeRadius && node.vy > 0) {
                node.vy = Math.min(node.vy, 2); // Allow some downward movement
              }
            });

            // Always nudge the simulation when scroll occurs
            if (scrollSim) {
              scrollSim.alpha(Math.max(0.05, currentAlpha + 0.03));
            }
          }
        }

        scrollDataRef.current.lastScrollY = currentScrollY;
        scrollDataRef.current.lastTime = currentTime;
        animationFrameRef.current = null;
      });
    };

    // Initialize scroll data
    scrollDataRef.current = { lastScrollY: window.scrollY, lastTime: Date.now() };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [data.nodes, debouncedControls, containerHeight, margin]);

  /* pill renderer with dynamic sizing */
  const draw = useCallback((
    n: Node,
    ctx: CanvasRenderingContext2D,
    s: number,
    mask?: string
  ) => {
    if (n.x == null || n.y == null) return;      // not yet positioned

    const sizeMultiplier = debouncedControls.nodeSizeMultiplier;
    const font = (18 * sizeMultiplier) / s;
    const padX = (12 * sizeMultiplier) / s;
    const padY = (6 * sizeMultiplier) / s;

    ctx.font = `600 ${font}px Inter, sans-serif`;
    const w = ctx.measureText(n.id).width + padX * 2;
    const h = font + padY * 2;
    const x = n.x - w / 2, y = n.y - h / 2, r = (9 * sizeMultiplier) / s;

    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.arcTo(x + w, y,     x + w, y + h, r);
    ctx.arcTo(x + w, y + h, x,     y + h, r);
    ctx.arcTo(x,     y + h, x,     y,     r);
    ctx.arcTo(x,     y,     x + w, y,     r);
    ctx.closePath();

    if (mask) { ctx.fillStyle = mask; ctx.fill(); return; }

    const col = colorOf[n.group as Cat];
    ctx.fillStyle   = `${col}22`;
    ctx.strokeStyle = col;
    ctx.lineWidth   = (1 * sizeMultiplier) / s;
    ctx.fill(); ctx.stroke();

    ctx.fillStyle = col;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(n.id, n.x, n.y);
  }, [debouncedControls.nodeSizeMultiplier]);

  // Control slider component
  const ControlSlider = ({ label, value, min, max, step, onChange }: {
    label: string;
    value: number;
    min: number;
    max: number;
    step: number;
    onChange: (value: number) => void;
  }) => (
    <div className="mb-3">
      <label className="block text-xs text-gray-400 mb-1">{label}: {value.toFixed(2)}</label>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        className="w-full h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
      />
    </div>
  );

  return (
    <div className="mx-auto relative" style={{ width: containerWidth }}>
      {/* Visual boundary container */}
      <div
        className="absolute inset-0 border border-white/10 rounded-lg pointer-events-none"
        style={{ width: containerWidth, height: containerHeight }}
      />

      {/* Physics Controls Panel */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={() => setShowControls(!showControls)}
          className="bg-gray-800 text-white px-3 py-2 rounded-lg text-sm border border-gray-600 hover:bg-gray-700"
        >
          {showControls ? 'Hide' : 'Show'} Controls
        </button>

        {showControls && (
          <div className="mt-2 bg-gray-900 border border-gray-600 rounded-lg p-4 w-64 max-h-96 overflow-y-auto">
            <h3 className="text-white text-sm font-semibold mb-3">Physics Controls</h3>

            <ControlSlider
              label="Node Size"
              value={controls.nodeSizeMultiplier}
              min={0.5}
              max={2.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, nodeSizeMultiplier: value }))}
            />

            <ControlSlider
              label="Repulsion Force"
              value={controls.repulsionStrength}
              min={100}
              max={1000}
              step={50}
              onChange={(value) => setControls(prev => ({ ...prev, repulsionStrength: value }))}
            />

            <ControlSlider
              label="Category Link Distance"
              value={controls.categoryLinkDistance}
              min={200}
              max={600}
              step={25}
              onChange={(value) => setControls(prev => ({ ...prev, categoryLinkDistance: value }))}
            />

            <ControlSlider
              label="Skill Link Distance"
              value={controls.skillLinkDistance}
              min={200}
              max={600}
              step={25}
              onChange={(value) => setControls(prev => ({ ...prev, skillLinkDistance: value }))}
            />

            <ControlSlider
              label="Category Link Strength"
              value={controls.categoryLinkStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, categoryLinkStrength: value }))}
            />

            <ControlSlider
              label="Skill Link Strength"
              value={controls.skillLinkStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, skillLinkStrength: value }))}
            />

            <ControlSlider
              label="Boundary Repulsion"
              value={controls.boundaryStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, boundaryStrength: value }))}
            />

            <ControlSlider
              label="Gravity Sensitivity"
              value={controls.gravityStrength}
              min={0.1}
              max={2.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, gravityStrength: value }))}
            />

            <ControlSlider
              label="Mass Multiplier"
              value={controls.massMultiplier}
              min={0.5}
              max={5.0}
              step={0.5}
              onChange={(value) => setControls(prev => ({ ...prev, massMultiplier: value }))}
            />

            <ControlSlider
              label="Cohesion Strength"
              value={controls.cohesionStrength}
              min={0.1}
              max={1.0}
              step={0.1}
              onChange={(value) => setControls(prev => ({ ...prev, cohesionStrength: value }))}
            />
          </div>
        )}
      </div>

      <ForceGraph2D
        ref={fgRef as any}
        width={containerWidth}
        height={containerHeight}
        backgroundColor="rgba(0,0,0,0)"
        graphData={data}
        nodeCanvasObject={(n, ctx, s) => draw(n as Node, ctx, s)}
        nodePointerAreaPaint={(n, c, ctx, s) => draw(n as Node, ctx, s, c)}
        linkColor={() => '#334155'}
        linkWidth={0.6}

        // Enable selective interactions
        enableNodeDrag={true}
        enablePanInteraction={false}
        enableZoomInteraction={false}

        // Stable node drag handlers
        onNodeDrag={(node: any) => {
          node.fx = node.x;
          node.fy = node.y;
          // Don't reheat during drag - let the simulation continue naturally
        }}
        onNodeDragEnd={(node: any) => {
          node.fx = undefined;
          node.fy = undefined;
          // Gentle restart instead of full reheat
          const simulation = fgRef.current?.d3Force('simulation');
          if (simulation) {
            simulation.alpha(Math.min(0.1, (simulation.alpha() || 0) + 0.05)).restart();
          }
        }}
      />
    </div>
  );
}
